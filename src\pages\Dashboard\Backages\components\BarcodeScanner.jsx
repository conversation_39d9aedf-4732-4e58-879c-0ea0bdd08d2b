import { useState, useEffect, useRef } from 'react';
import { Dialog } from 'primereact/dialog';
import Quagga from '@ericblade/quagga2';
import { motion } from 'framer-motion';
import './BarcodeScanner.css';

const BarcodeScanner = ({ isOpen, onClose, onBarcodeScanned }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [scannedResult, setScannedResult] = useState('');
  const scannerContainerRef = useRef(null);
  const onDetectedRef = useRef(null);

  useEffect(() => {
    if (isOpen) {
      initializeScanner();
    } else {
      cleanup();
    }

    return () => cleanup();
  }, [isOpen]);

  const initializeScanner = async () => {
    setIsLoading(true);
    setError(null);
    setScannedResult('');

    try {
      // Wait for the scanner container to be mounted and attached
      await new Promise((resolve, reject) => {
        let tries = 0;
        const maxTries = 30; // ~0.5s if using rAF
        const check = () => {
          if (scannerContainerRef.current && document.body.contains(scannerContainerRef.current)) {
            resolve();
          } else if (tries++ > maxTries) {
            reject(new Error('Scanner container not available'));
          } else {
            requestAnimationFrame(check);
          }
        };
        check();
      });

      const constraints = {
        facingMode: { ideal: 'environment' },
        width: { ideal: 640 },
        height: { ideal: 480 },
        aspectRatio: { ideal: 1.3333333333 }
      };

      await new Promise((resolve, reject) => {
        Quagga.init(
          {
            inputStream: {
              type: 'LiveStream',
              target: scannerContainerRef.current,
              constraints,
              area: { top: '0%', right: '0%', left: '0%', bottom: '0%' }
            },
            locator: { patchSize: 'medium', halfSample: true },
            numOfWorkers: navigator.hardwareConcurrency ? Math.min(4, navigator.hardwareConcurrency) : 2,
            decoder: {
              readers: ['code_93_reader']
            },
            locate: true
          },
          (err) => {
            if (err) return reject(err);
            return resolve();
          }
        );
      });

      onDetectedRef.current = (data) => {
        if (data?.codeResult?.code) {
          const format = data.codeResult.format;
          if (!format || format.toLowerCase() === 'code_93') {
            const scannedText = data.codeResult.code;
            setScannedResult(scannedText);
            onBarcodeScanned(scannedText);
            cleanup();
          }
        }
      };

      Quagga.onDetected(onDetectedRef.current);
      if (!Quagga.running) {
        Quagga.start();
      }
      setIsLoading(false);
    } catch (err) {
      console.error('Scanner initialization error:', err);

      if (err?.name === 'NotFoundError') {
        onClose();
        return;
      } else if (err?.name === 'NotAllowedError' || err?.name === 'PermissionDeniedError') {
        setError('Camera access denied. Please allow camera access and try again.');
      } else if (err?.name === 'NotReadableError' || err?.name === 'TrackStartError') {
        setError('Camera is already in use by another application.');
      } else if (typeof err === 'string') {
        setError(err);
      } else {
        setError(err?.message || 'Failed to initialize camera');
      }

      try { if (Quagga.running) Quagga.stop(); } catch (_) {}
      setIsLoading(false);
    }
  };

  const cleanup = () => {
    try {
      if (onDetectedRef.current) {
        Quagga.offDetected(onDetectedRef.current);
        onDetectedRef.current = null;
      }
      if (Quagga.running) {
        Quagga.stop();
      }
    } catch (_) {}

    if (scannerContainerRef.current) {
      // Remove Quagga's injected nodes safely
      while (scannerContainerRef.current.firstChild) {
        scannerContainerRef.current.removeChild(scannerContainerRef.current.firstChild);
      }
    }
  };

  const handleClose = () => {
    cleanup();
    onClose();
  };

  return (
    <Dialog
      header={
        <div className="flex items-center gap-3">
          <div className="w-1 h-8 bg-gradient-to-b from-[#00c3ac] to-[#02aa96] rounded-full"></div>
          <h2 className="text-xl font-semibold text-gray-900">Scan Barcode</h2>
        </div>
      }
      visible={isOpen}
      onHide={handleClose}
      style={{
        width: "90vw",
        maxWidth: "600px",
        borderRadius: "16px",
        overflow: "hidden"
      }}
      modal
      className="barcode-scanner-dialog"
      contentClassName="p-0"
    >
      <div className="p-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="space-y-4"
        >
          <div className="text-center mb-4">
            <p className="text-gray-600 text-sm">
              Position a Code 93 barcode within the camera view to scan
            </p>
          </div>

          <div className="relative bg-black rounded-lg overflow-hidden" style={{ aspectRatio: '4/3' }}>
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                <div className="text-center">
                  <div className="w-8 h-8 border-4 border-[#00c3ac] border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                  <p className="text-gray-600 text-sm">Initializing camera...</p>
                </div>
              </div>
            )}

            {error && (
              <div className="absolute inset-0 flex items-center justify-center bg-red-50">
                <div className="text-center p-4">
                  <div className="text-red-500 mb-2">
                    <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <p className="text-red-700 text-sm font-medium">{error}</p>
                </div>
              </div>
            )}

            <div ref={scannerContainerRef} className="w-full h-full" />

            {!isLoading && !error && (
              <div className="absolute inset-0 pointer-events-none">
                <div className="absolute inset-0 border-2 border-[#00c3ac] opacity-50"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-32 border-2 border-[#00c3ac] rounded-lg"></div>
              </div>
            )}
          </div>

          {scannedResult && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <p className="text-green-800 text-sm font-medium">Scanned Result:</p>
              <p className="text-green-700 font-mono text-sm break-all">{scannedResult}</p>
            </div>
          )}

          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
            <motion.button
              type="button"
              onClick={handleClose}
              className="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-all duration-200"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Close
            </motion.button>
            
            {error && (
              <motion.button
                type="button"
                onClick={initializeScanner}
                className="px-6 py-3 bg-[#00c3ac] hover:bg-[#02aa96] text-white rounded-lg font-medium transition-all duration-200"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Retry
              </motion.button>
            )}
          </div>
        </motion.div>
      </div>
    </Dialog>
  );
};

export default BarcodeScanner;